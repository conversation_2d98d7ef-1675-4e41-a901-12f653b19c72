'use client';

import { useEffect, useState, useRef } from 'react';

// Add shimmer animation styles
const shimmerStyles = `
  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }
  .animate-shimmer {
    animation: shimmer 2s infinite linear;
  }
`;
import { Product } from '@/types/user';
import { formatCurrency, formatDate } from '@/lib/utils';
import { XMarkIcon, TagIcon, CubeIcon, CalendarIcon } from '@heroicons/react/24/outline';
import { IconButton } from '@/components/ui/Button';
import Card, { CardContent, CardHeader } from '@/components/ui/Card';
import ImageGallery from '@/components/ui/ImageGallery';

interface ProductDetailModalProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
}

export default function ProductDetailModal({ product, isOpen, onClose }: ProductDetailModalProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const closeButtonRef = useRef<HTMLButtonElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);

  // Enhanced close handler with animation
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 200); // Match animation duration
  };

  // Handle escape key, body scroll, and focus management
  useEffect(() => {
    if (isOpen) {
      // Store the previously focused element
      previousActiveElement.current = document.activeElement as HTMLElement;

      // Prevent body scroll
      document.body.style.overflow = 'hidden';

      // Focus the close button after modal opens
      const focusTimer = setTimeout(() => {
        closeButtonRef.current?.focus();
      }, 100);

      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          handleClose();
        }
      };

      // Handle focus trap
      const handleTabKey = (e: KeyboardEvent) => {
        if (e.key === 'Tab' && modalRef.current) {
          const focusableElements = modalRef.current.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          );
          const firstElement = focusableElements[0] as HTMLElement;
          const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

          if (e.shiftKey) {
            if (document.activeElement === firstElement) {
              e.preventDefault();
              lastElement?.focus();
            }
          } else {
            if (document.activeElement === lastElement) {
              e.preventDefault();
              firstElement?.focus();
            }
          }
        }
      };

      document.addEventListener('keydown', handleEscape);
      document.addEventListener('keydown', handleTabKey);

      // Simulate loading state and error handling
      const loadingTimer = setTimeout(() => {
        try {
          // Basic validation
          if (!product || !product.name) {
            setHasError(true);
          }
          setIsLoading(false);
        } catch (error) {
          console.error('Error loading product details:', error);
          setHasError(true);
          setIsLoading(false);
        }
      }, 300);

      return () => {
        document.body.style.overflow = 'unset';
        document.removeEventListener('keydown', handleEscape);
        document.removeEventListener('keydown', handleTabKey);
        clearTimeout(focusTimer);
        clearTimeout(loadingTimer);

        // Restore focus to the previously focused element
        if (previousActiveElement.current) {
          previousActiveElement.current.focus();
        }
      };
    }
  }, [isOpen, onClose]);

  // Get product type styling
  const getTypeConfig = (type: string) => {
    switch (type) {
      case 'featured':
        return {
          label: 'Featured',
          className: 'bg-purple-100 text-purple-800 border-purple-200',
          icon: '⭐'
        };
      case 'sale':
        return {
          label: 'On Sale',
          className: 'bg-red-100 text-red-800 border-red-200',
          icon: '🏷️'
        };
      case 'new':
        return {
          label: 'New',
          className: 'bg-green-100 text-green-800 border-green-200',
          icon: '✨'
        };
      default:
        return {
          label: 'Regular',
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: '📦'
        };
    }
  };

  // Get stock status styling
  const getStockConfig = (stock: boolean) => {
    return stock
      ? {
          label: 'In Stock',
          className: 'bg-green-100 text-green-800 border-green-200',
          icon: '✅'
        }
      : {
          label: 'Out of Stock',
          className: 'bg-red-100 text-red-800 border-red-200',
          icon: '❌'
        };
  };

  // Prepare images array for gallery with error handling
  const allImages = [product?.image, ...(product?.images || [])].filter(Boolean);

  // Handle image loading errors
  const handleImageError = () => {
    setImageError(true);
  };

  // Reset error states when modal opens
  useEffect(() => {
    if (isOpen) {
      setImageError(false);
      setHasError(false);
      setIsLoading(true);
    }
  }, [isOpen, product]);

  if (!isOpen) return null;

  return (
    <>
      {/* Inject shimmer animation styles */}
      <style dangerouslySetInnerHTML={{ __html: shimmerStyles }} />

      <div
        className="fixed inset-0 z-50 overflow-y-auto"
        role="dialog"
        aria-modal="true"
        aria-labelledby="product-modal-title"
      >
      {/* Backdrop */}
      <div
        className={`fixed inset-0 bg-black/60 backdrop-blur-sm transition-all duration-300 ${
          isClosing ? 'opacity-0' : 'opacity-100'
        }`}
        onClick={handleClose}
        aria-hidden="true"
      />

      {/* Modal */}
      <div className="flex min-h-full items-end sm:items-center justify-center p-0 sm:p-4 lg:p-6">
        <div
          ref={modalRef}
          className={`relative w-full max-w-4xl transform transition-all duration-300 ease-out ${
            isClosing
              ? 'scale-95 opacity-0 translate-y-4 sm:translate-y-0'
              : 'scale-100 opacity-100 translate-y-0'
          }`}
          role="document"
        >
          <Card className="overflow-hidden rounded-none sm:rounded-xl max-h-screen sm:max-h-[90vh] flex flex-col p-0 shadow-2xl">
            {/* Header */}
            <CardHeader className="flex flex-row items-center justify-between border-b border-gray-200 bg-gray-50/50 px-4 py-3 sm:px-6 sm:py-4 flex-shrink-0">
              <div className="flex items-center space-x-2 sm:space-x-3 min-w-0">
                <div className="p-1.5 sm:p-2 bg-blue-100 rounded-lg flex-shrink-0">
                  <CubeIcon className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" />
                </div>
                <div className="min-w-0">
                  <h2
                    id="product-modal-title"
                    className="text-lg sm:text-xl font-semibold text-gray-900 truncate"
                  >
                    Product Details
                  </h2>
                  <p className="text-xs sm:text-sm text-gray-500 hidden sm:block">
                    View complete product information
                  </p>
                </div>
              </div>

              <IconButton
                ref={closeButtonRef}
                icon={<XMarkIcon className="w-5 h-5" />}
                onClick={handleClose}
                aria-label="Close product details modal (Escape key)"
                className="hover:bg-gray-100 hover:scale-105 active:scale-95 transition-all duration-150 flex-shrink-0 ml-2"
              />
            </CardHeader>

            {/* Content */}
            <CardContent className="p-4 sm:p-6 flex-1 overflow-y-auto">
              {isLoading ? (
                // Enhanced Loading skeleton
                <div className="animate-pulse space-y-4 sm:space-y-6">
                  <div className="flex flex-col lg:flex-row gap-4 sm:gap-6">
                    {/* Image skeleton */}
                    <div className="lg:w-1/2">
                      <div className="aspect-square bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer rounded-xl"></div>
                      {/* Thumbnail skeletons */}
                      <div className="flex space-x-2 mt-3 sm:mt-4">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-200 rounded-lg"></div>
                        ))}
                      </div>
                    </div>

                    {/* Content skeleton */}
                    <div className="lg:w-1/2 space-y-3 sm:space-y-4">
                      {/* Title skeleton */}
                      <div className="space-y-2">
                        <div className="h-6 sm:h-8 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-4 sm:h-6 bg-gray-200 rounded w-1/2"></div>
                      </div>

                      {/* Badges skeleton */}
                      <div className="flex gap-2">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="h-6 sm:h-8 bg-gray-200 rounded-full w-16 sm:w-20"></div>
                        ))}
                      </div>

                      {/* Price skeleton */}
                      <div className="bg-gray-100 rounded-xl p-3 sm:p-4">
                        <div className="h-8 sm:h-10 bg-gray-200 rounded w-32 sm:w-40"></div>
                        <div className="h-3 sm:h-4 bg-gray-200 rounded w-24 sm:w-32 mt-2"></div>
                      </div>

                      {/* Details grid skeleton */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="bg-gray-100 rounded-lg p-3 sm:p-4">
                            <div className="h-4 bg-gray-200 rounded w-16 mb-2"></div>
                            <div className="h-5 bg-gray-200 rounded w-20"></div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Description skeleton */}
                  <div className="border-t border-gray-200 pt-4 sm:pt-6">
                    <div className="h-5 sm:h-6 bg-gray-200 rounded w-40 mb-3"></div>
                    <div className="bg-gray-100 rounded-xl p-3 sm:p-4">
                      <div className="space-y-2">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="h-4 bg-gray-200 rounded"></div>
                        ))}
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : hasError ? (
                // Error state
                <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Unable to Load Product Details
                  </h3>
                  <p className="text-gray-600 mb-4 max-w-md">
                    We're having trouble loading the product information. Please try again.
                  </p>
                  <button
                    onClick={() => {
                      setHasError(false);
                      setIsLoading(true);
                      // Retry loading
                      setTimeout(() => setIsLoading(false), 300);
                    }}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    Try Again
                  </button>
                </div>
              ) : (
                <div className="space-y-4 sm:space-y-6">
                  {/* Main Content Grid */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                    {/* Image Gallery Section */}
                    <div className="space-y-3 sm:space-y-4 order-1 lg:order-1">
                      <ImageGallery
                        images={allImages}
                        alt={product.name}
                        className="w-full"
                      />
                    </div>

                    {/* Product Information Section */}
                    <div className="space-y-4 sm:space-y-6 order-2 lg:order-2">
                      {/* Product Title and Badges */}
                      <div className="space-y-2 sm:space-y-3">
                        <h3 className="text-xl sm:text-2xl font-bold text-gray-900 leading-tight break-words">
                          {product.name}
                        </h3>

                        <div className="flex flex-wrap gap-1.5 sm:gap-2" role="group" aria-label="Product badges">
                          {/* Product Type Badge */}
                          <span
                            className={`inline-flex items-center gap-1 sm:gap-1.5 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full text-xs sm:text-sm font-medium border transition-all duration-200 hover:scale-105 cursor-default ${getTypeConfig(product.type).className}`}
                            role="status"
                            aria-label={`Product type: ${getTypeConfig(product.type).label}`}
                          >
                            <span className="text-sm sm:text-base" aria-hidden="true">{getTypeConfig(product.type).icon}</span>
                            <span className="hidden xs:inline">{getTypeConfig(product.type).label}</span>
                          </span>

                          {/* Stock Status Badge */}
                          <span
                            className={`inline-flex items-center gap-1 sm:gap-1.5 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full text-xs sm:text-sm font-medium border transition-all duration-200 hover:scale-105 cursor-default ${getStockConfig(product.stock).className}`}
                            role="status"
                            aria-label={`Stock status: ${getStockConfig(product.stock).label}`}
                          >
                            <span className="text-sm sm:text-base" aria-hidden="true">{getStockConfig(product.stock).icon}</span>
                            <span className="hidden xs:inline">{getStockConfig(product.stock).label}</span>
                          </span>

                          {/* Discount Badge */}
                          {product.discount && (
                            <span
                              className="inline-flex items-center gap-1 sm:gap-1.5 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full text-xs sm:text-sm font-medium border bg-orange-100 text-orange-800 border-orange-200 transition-all duration-200 hover:scale-105 cursor-default animate-pulse"
                              role="status"
                              aria-label={`Discount: ${product.discount}% off`}
                            >
                              <span className="text-sm sm:text-base" aria-hidden="true">🏷️</span>
                              <span className="hidden xs:inline">{product.discount}% OFF</span>
                              <span className="xs:hidden">{product.discount}%</span>
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Price Section */}
                      <div
                        className="bg-gray-50 rounded-xl p-3 sm:p-4"
                        role="region"
                        aria-labelledby="price-section"
                      >
                        <h4 id="price-section" className="sr-only">Product Pricing</h4>
                        <div className="flex flex-col xs:flex-row xs:items-baseline gap-1 xs:gap-3">
                          <span
                            className="text-2xl sm:text-3xl font-bold text-gray-900"
                            aria-label={`Current price: ${formatCurrency(product.price)}`}
                          >
                            {formatCurrency(product.price)}
                          </span>
                          {product.discount && (
                            <span
                              className="text-base sm:text-lg text-gray-500 line-through"
                              aria-label={`Original price: ${formatCurrency(product.price / (1 - parseFloat(product.discount) / 100))}`}
                            >
                              {formatCurrency(product.price / (1 - parseFloat(product.discount) / 100))}
                            </span>
                          )}
                        </div>
                        {product.discount && (
                          <p
                            className="text-xs sm:text-sm text-green-600 font-medium mt-1"
                            aria-label={`Savings: ${formatCurrency(product.price / (1 - parseFloat(product.discount) / 100) - product.price)}`}
                          >
                            You save {formatCurrency(product.price / (1 - parseFloat(product.discount) / 100) - product.price)}
                          </p>
                        )}
                      </div>

                      {/* Product Details Grid */}
                      <div
                        className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4"
                        role="group"
                        aria-labelledby="product-details-heading"
                      >
                        <h4 id="product-details-heading" className="sr-only">Product Details</h4>

                        <div
                          className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 transition-all duration-200 hover:shadow-md hover:border-gray-300"
                          role="group"
                          aria-labelledby="category-label"
                        >
                          <div className="flex items-center gap-1.5 sm:gap-2 mb-1.5 sm:mb-2">
                            <TagIcon className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" aria-hidden="true" />
                            <span id="category-label" className="text-xs sm:text-sm font-medium text-gray-700">Category</span>
                          </div>
                          <p className="text-sm sm:text-base text-gray-900 font-medium truncate" aria-describedby="category-label">
                            {product.category}
                          </p>
                        </div>

                        <div
                          className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 transition-all duration-200 hover:shadow-md hover:border-gray-300"
                          role="group"
                          aria-labelledby="quantity-label"
                        >
                          <div className="flex items-center gap-1.5 sm:gap-2 mb-1.5 sm:mb-2">
                            <CubeIcon className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" aria-hidden="true" />
                            <span id="quantity-label" className="text-xs sm:text-sm font-medium text-gray-700">Quantity</span>
                          </div>
                          <p className="text-sm sm:text-base text-gray-900 font-medium" aria-describedby="quantity-label">
                            {product.quantity} units
                          </p>
                        </div>

                        <div
                          className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 sm:col-span-2 transition-all duration-200 hover:shadow-md hover:border-gray-300"
                          role="group"
                          aria-labelledby="created-label"
                        >
                          <div className="flex items-center gap-1.5 sm:gap-2 mb-1.5 sm:mb-2">
                            <CalendarIcon className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" aria-hidden="true" />
                            <span id="created-label" className="text-xs sm:text-sm font-medium text-gray-700">Created</span>
                          </div>
                          <p className="text-sm sm:text-base text-gray-900 font-medium" aria-describedby="created-label">
                            <time dateTime={product.createdAt}>
                              {formatDate(new Date(product.createdAt))}
                            </time>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Description Section */}
                  {product.description && (
                    <section
                      className="border-t border-gray-200 pt-4 sm:pt-6"
                      aria-labelledby="description-heading"
                    >
                      <h4
                        id="description-heading"
                        className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3"
                      >
                        Product Description
                      </h4>
                      <div className="bg-gray-50 rounded-xl p-3 sm:p-4">
                        <p
                          className="text-sm sm:text-base text-gray-700 leading-relaxed whitespace-pre-wrap"
                          aria-describedby="description-heading"
                        >
                          {product.description}
                        </p>
                      </div>
                    </section>
                  )}
                </div>
              )}
            </CardContent>

            {/* Footer */}
            <div className="border-t border-gray-200 px-4 py-3 sm:px-6 sm:py-4 bg-gray-50/50 flex-shrink-0">
              <div className="flex justify-end">
                <button
                  onClick={onClose}
                  className="w-full sm:w-auto px-4 sm:px-6 py-2.5 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 touch-manipulation"
                >
                  Close
                </button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
    </>
  );
}
