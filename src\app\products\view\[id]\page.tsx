'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { ArrowLeftIcon, ShoppingCartIcon, HeartIcon, ShareIcon } from '@heroicons/react/24/outline';
import { CheckCircleIcon, XCircleIcon, TagIcon, CubeIcon, CalendarIcon, TruckIcon } from '@heroicons/react/24/solid';
import DashboardLayout from '@/components/layout/DashboardLayout';
import productService from '@/lib/api/productService';
import { Product } from '@/types/user';
import { formatCurrency, formatDate } from '@/lib/utils';
import ImageGallery from '@/components/ui/ImageGallery';
import { toast } from 'sonner';

export default function ProductDetailPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await productService.getProduct(productId);
        
        if (response.status === 'success') {
          setProduct(response.data);
        } else {
          throw new Error('Failed to fetch product');
        }
      } catch (err: unknown) {
        console.error('Error fetching product:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch product');
      } finally {
        setLoading(false);
      }
    };

    if (productId) {
      fetchProduct();
    }
  }, [productId]);

  // Get product type configuration with functional icons
  const getTypeConfig = (type: string) => {
    switch (type) {
      case 'featured':
        return {
          label: 'Featured Product',
          className: 'bg-purple-50 text-purple-700 border-purple-200',
          icon: <TagIcon className="w-4 h-4" />
        };
      case 'sale':
        return {
          label: 'On Sale',
          className: 'bg-red-50 text-red-700 border-red-200',
          icon: <TagIcon className="w-4 h-4" />
        };
      case 'new':
        return {
          label: 'New Arrival',
          className: 'bg-green-50 text-green-700 border-green-200',
          icon: <TagIcon className="w-4 h-4" />
        };
      default:
        return {
          label: 'Regular Product',
          className: 'bg-gray-50 text-gray-700 border-gray-200',
          icon: <CubeIcon className="w-4 h-4" />
        };
    }
  };

  // Get stock status configuration with functional icons
  const getStockConfig = (stock: boolean) => {
    return stock
      ? {
          label: 'In Stock',
          className: 'bg-green-50 text-green-700 border-green-200',
          icon: <CheckCircleIcon className="w-4 h-4" />
        }
      : {
          label: 'Out of Stock',
          className: 'bg-red-50 text-red-700 border-red-200',
          icon: <XCircleIcon className="w-4 h-4" />
        };
  };

  // Handle back navigation
  const handleBack = () => {
    router.back();
  };

  // Handle edit navigation
  const handleEdit = () => {
    router.push(`/products/edit/${productId}`);
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen w-full">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            {/* Loading skeleton */}
            <div className="animate-pulse space-y-6">
              {/* Header skeleton */}
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                <div className="h-8 bg-gray-200 rounded w-48"></div>
              </div>
              
              {/* Main content skeleton */}
              <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 xl:gap-12">
                {/* Image section skeleton */}
                <div className="xl:col-span-2">
                  <div className="bg-white rounded-xl p-6 shadow-sm">
                    <div className="aspect-square bg-gray-200 rounded-xl"></div>
                    <div className="flex space-x-2 mt-4">
                      {[...Array(4)].map((_, i) => (
                        <div key={i} className="w-16 h-16 bg-gray-200 rounded-lg"></div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Info section skeleton */}
                <div className="space-y-6">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="bg-white rounded-xl p-6 shadow-sm">
                      <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
                      <div className="space-y-3">
                        <div className="h-4 bg-gray-200 rounded"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !product) {
    return (
      <DashboardLayout>
        <div className="min-h-screen w-full">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="text-center py-12">
              <XCircleIcon className="w-16 h-16 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Error Loading Product
              </h3>
              <p className="text-red-600 mb-4">{error || 'Product not found'}</p>
              <button
                onClick={handleBack}
                className="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Prepare images for gallery
  const allImages = [product.image, ...(product.images || [])].filter(Boolean);

  return (
    <DashboardLayout>
      <div className="min-h-screen w-full bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <div className="flex items-center gap-4">
              <button
                onClick={handleBack}
                className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-lg transition-colors"
                aria-label="Go back to products list"
              >
                <ArrowLeftIcon className="w-5 h-5" />
                <span className="hidden sm:inline">Back to Products</span>
              </button>
              <div className="h-6 w-px bg-gray-300 hidden sm:block"></div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Product Details</h1>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={handleEdit}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base"
              >
                <span className="sm:hidden">Edit</span>
                <span className="hidden sm:inline">Edit Product</span>
              </button>
            </div>
          </div>

          {/* Main Content - Optimized for Large Screens */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 xl:gap-12">
            {/* Image Gallery Section - Takes 2/3 width on XL screens */}
            <div className="xl:col-span-2">
              <div className="bg-white rounded-xl shadow-sm overflow-hidden sticky top-6">
                <ImageGallery
                  images={allImages}
                  alt={product.name}
                  className="w-full"
                />
              </div>
            </div>

            {/* Product Information Section - Takes 1/3 width on XL screens */}
            <div className="space-y-6">
              {/* Product Title and Badges */}
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <div className="space-y-4">
                  <div>
                    <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 leading-tight mb-2">
                      {product.name}
                    </h2>
                    <p className="text-gray-600">Product ID: {product._id.slice(-8).toUpperCase()}</p>
                  </div>

                  {/* Status Badges */}
                  <div className="flex flex-wrap gap-2" role="group" aria-label="Product status indicators">
                    {/* Product Type Badge */}
                    <span
                      className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium border ${getTypeConfig(product.type).className}`}
                      role="status"
                      aria-label={`Product type: ${getTypeConfig(product.type).label}`}
                    >
                      {getTypeConfig(product.type).icon}
                      {getTypeConfig(product.type).label}
                    </span>

                    {/* Stock Status Badge */}
                    <span
                      className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium border ${getStockConfig(product.stock).className}`}
                      role="status"
                      aria-label={`Stock status: ${getStockConfig(product.stock).label}`}
                    >
                      {getStockConfig(product.stock).icon}
                      {getStockConfig(product.stock).label}
                    </span>

                    {/* Discount Badge */}
                    {product.discount && (
                      <span
                        className="inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium border bg-orange-50 text-orange-700 border-orange-200"
                        role="status"
                        aria-label={`Discount: ${product.discount}% off`}
                      >
                        <TagIcon className="w-4 h-4" />
                        {product.discount}% OFF
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Pricing Section */}
              <div className="bg-white rounded-xl p-6 shadow-sm border-l-4 border-l-blue-500">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <TagIcon className="w-5 h-5 text-blue-600" />
                  Pricing Information
                </h3>
                <div className="space-y-4">
                  <div className="flex flex-col gap-2">
                    <div className="flex items-baseline gap-3">
                      <span
                        className="text-4xl font-bold text-gray-900"
                        aria-label={`Current price: ${formatCurrency(product.price)}`}
                      >
                        {formatCurrency(product.price)}
                      </span>
                      {product.discount && (
                        <span
                          className="text-xl text-gray-500 line-through"
                          aria-label={`Original price: ${formatCurrency(product.price / (1 - parseFloat(product.discount) / 100))}`}
                        >
                          {formatCurrency(product.price / (1 - parseFloat(product.discount) / 100))}
                        </span>
                      )}
                    </div>
                    {product.discount && (
                      <div className="bg-green-50 rounded-lg p-3 border border-green-200">
                        <p className="text-sm text-green-700 font-medium flex items-center gap-2">
                          <CheckCircleIcon className="w-4 h-4" />
                          You save {formatCurrency(product.price / (1 - parseFloat(product.discount) / 100) - product.price)} ({product.discount}% off)
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Price per unit if quantity > 1 */}
                  {product.quantity > 1 && (
                    <div className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                      <span className="font-medium">Price per unit:</span> {formatCurrency(product.price)}
                    </div>
                  )}
                </div>
              </div>

              {/* Product Details */}
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Details</h3>
                <div className="space-y-4">
                  {/* Category */}
                  <div className="flex items-center justify-between py-3 border-b border-gray-100">
                    <div className="flex items-center gap-2 text-gray-600">
                      <TagIcon className="w-4 h-4" />
                      <span className="font-medium">Category</span>
                    </div>
                    <span className="text-gray-900 font-medium">{product.category}</span>
                  </div>

                  {/* Quantity */}
                  <div className="flex items-center justify-between py-3 border-b border-gray-100">
                    <div className="flex items-center gap-2 text-gray-600">
                      <CubeIcon className="w-4 h-4" />
                      <span className="font-medium">Available Quantity</span>
                    </div>
                    <span className="text-gray-900 font-medium">{product.quantity} units</span>
                  </div>

                  {/* Created Date */}
                  <div className="flex items-center justify-between py-3">
                    <div className="flex items-center gap-2 text-gray-600">
                      <CalendarIcon className="w-4 h-4" />
                      <span className="font-medium">Added</span>
                    </div>
                    <span className="text-gray-900 font-medium">
                      {formatDate(new Date(product.createdAt))}
                    </span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <div className="space-y-3">
                  <button
                    onClick={handleEdit}
                    className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                  >
                    Edit Product
                  </button>

                  <div className="grid grid-cols-2 gap-3">
                    <button
                      className="flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                      aria-label="Add to favorites"
                    >
                      <HeartIcon className="w-4 h-4" />
                      <span className="hidden lg:inline">Favorite</span>
                    </button>
                    <button
                      className="flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                      aria-label="Share product"
                    >
                      <ShareIcon className="w-4 h-4" />
                      <span className="hidden lg:inline">Share</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Description Section - Full Width Below */}
          {product.description && (
            <div className="mt-8">
              <div className="bg-white rounded-xl p-6 lg:p-8 shadow-sm">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Product Description
                </h3>
                <div className="prose max-w-none">
                  <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                    {product.description}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Additional Information Section */}
          <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {/* Shipping Info */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-blue-50 rounded-lg">
                  <TruckIcon className="w-5 h-5 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-900">Shipping</h4>
              </div>
              <p className="text-gray-600 text-sm">
                Free shipping on orders over $50. Standard delivery in 3-5 business days.
              </p>
            </div>

            {/* Return Policy */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-green-50 rounded-lg">
                  <CheckCircleIcon className="w-5 h-5 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-900">Returns</h4>
              </div>
              <p className="text-gray-600 text-sm">
                30-day return policy. Items must be in original condition.
              </p>
            </div>

            {/* Support */}
            <div className="bg-white rounded-xl p-6 shadow-sm lg:col-span-2 xl:col-span-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-purple-50 rounded-lg">
                  <HeartIcon className="w-5 h-5 text-purple-600" />
                </div>
                <h4 className="font-semibold text-gray-900">Support</h4>
              </div>
              <p className="text-gray-600 text-sm">
                24/7 customer support available. Contact us for any questions.
              </p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
